# 遊戲安裝器 (Game Installer)

一個使用C++和Qt開發的現代化遊戲安裝器，具有蘋果淡色風格的用戶界面。

## 功能特色

- 🎮 自動掃描games資料夾中的ZIP遊戲檔案
- 📦 一鍵安裝遊戲到指定位置（預設：C:/game）
- 🎨 現代化蘋果淡色風格UI設計
- 🃏 卡牌式網格布局，水平優先排列（先由左而右，再由上往下）
- 📱 響應式設計，自動適應窗口大小，動態重排
- 📊 即時顯示安裝進度
- 📁 自動創建安裝目錄
- 💾 顯示檔案大小資訊
- ✨ 平滑懸停動畫效果

## 系統需求

- Windows 10/11
- Qt 6.9.1 或更高版本
- MinGW 64位編譯器

## 使用方法

### 快速開始

1. **放置遊戲檔案**：將ZIP格式的遊戲檔案放入 `games` 資料夾
2. **啟動應用程式**：雙擊 `run_game_installer.bat`
3. **安裝遊戲**：在界面中點擊想要安裝的遊戲旁邊的「安裝」按鈕

### 詳細說明

#### 遊戲檔案管理
- 支援的格式：`.zip`
- 放置位置：專案根目錄下的 `games` 資料夾
- 檔案命名：檔案名稱將作為遊戲名稱顯示

#### 安裝位置
- 預設安裝路徑：`C:/game/[遊戲名稱]/`
- 如果目標目錄不存在，程式會自動創建
- 每個遊戲都會安裝到獨立的子資料夾中

#### 界面功能
- **刷新按鈕**：重新掃描games資料夾
- **遊戲卡片**：卡牌式設計，顯示遊戲圖標、名稱、檔案大小和安裝按鈕
- **水平布局**：先由左而右，再由上往下的直觀排列順序
- **響應式網格**：自動調整每行卡片數量（1-6張），窗口大小改變時動態重排
- **進度條**：顯示當前安裝進度
- **狀態欄**：顯示當前操作狀態
- **懸停效果**：卡片懸停時有陰影和位移動畫

## 編譯說明

### 使用qmake編譯

```bash
# 設置環境變數
export PATH="/c/Qt/6.9.1/mingw_64/bin:/c/Qt/Tools/mingw1310_64/bin:$PATH"

# 生成Makefile
qmake GameInstaller.pro

# 編譯
mingw32-make
```

### 使用CMake編譯

```bash
# 創建build目錄
mkdir build && cd build

# 配置CMake
cmake -G "MinGW Makefiles" ..

# 編譯
mingw32-make
```

## 專案結構

```
GameInstaller/
├── main.cpp              # 程式入口點
├── mainwindow.h/cpp      # 主窗口類
├── gameinstaller.h/cpp   # 遊戲安裝器核心類
├── gamecard.h/cpp        # 遊戲卡片UI組件
├── mainwindow.ui         # Qt Designer UI檔案
├── GameInstaller.pro     # qmake專案檔案
├── CMakeLists.txt        # CMake專案檔案
├── run_game_installer.bat # Windows啟動腳本
├── games/                # 遊戲ZIP檔案目錄
│   └── TestGame.zip      # 範例遊戲檔案
└── bin/                  # 編譯輸出目錄
    └── GameInstaller.exe # 可執行檔案
```

## 技術特點

### UI設計
- 採用蘋果淡色風格設計語言
- 卡牌式網格佈局，每張卡片280x200像素
- 響應式設計，根據窗口寬度自動調整佈局
- 平滑的懸停效果和位移動畫
- 現代化的圓角按鈕和進度條樣式
- 遊戲圖標和清晰的視覺層次

### 核心功能
- 使用PowerShell的Expand-Archive進行ZIP解壓
- Qt信號槽機制處理異步操作
- 自動檔案系統管理
- 錯誤處理和用戶反饋

### 架構設計
- 模組化設計，職責分離
- GameInstaller類處理核心邏輯
- GameCard類處理UI展示
- MainWindow類協調整體流程

## 故障排除

### 常見問題

1. **應用程式無法啟動**
   - 確保Qt DLL在PATH中
   - 使用提供的 `run_game_installer.bat` 腳本

2. **找不到遊戲檔案**
   - 確認ZIP檔案放在 `games` 資料夾中
   - 點擊「刷新遊戲列表」按鈕

3. **安裝失敗**
   - 檢查目標磁碟空間
   - 確認有寫入權限
   - 檢查ZIP檔案是否損壞

## 授權

本專案採用MIT授權條款。

## 🎯 範例遊戲

程式已包含五個測試遊戲，展示卡牌布局效果：
- **TestGame.zip** - 小型測試檔案
- **AdventureGame.zip** - 冒險遊戲
- **RacingGame.zip** - 賽車遊戲
- **PuzzleGame.zip** - 益智遊戲
- **StrategyGame.zip** - 戰略遊戲

## 🎨 卡牌布局特色

### 響應式設計
- 窗口寬度 < 900px：每行1張卡片
- 窗口寬度 900-1200px：每行2-3張卡片
- 窗口寬度 > 1200px：每行4-6張卡片

### 視覺效果
- 卡片尺寸：280x200像素
- 圓角邊框：16px
- 懸停陰影：0 8px 24px rgba(0, 122, 255, 0.2)
- 懸停位移：向上2px
- 遊戲圖標：🎮 emoji，36px大小

## 貢獻

歡迎提交Issue和Pull Request來改進這個專案！
