#include "gameinstaller.h"
#include <QApplication>
#include <QStandardPaths>
#include <QDebug>
#include <QFileInfoList>

GameInstaller::GameInstaller(QObject *parent)
    : QObject(parent)
    , m_extractProcess(nullptr)
    , m_gamesDirectory(QApplication::applicationDirPath() + "/games")
{
}

QStringList GameInstaller::scanGameZipFiles()
{
    QStringList zipFiles;
    QDir gamesDir(m_gamesDirectory);
    
    // 如果games資料夾不存在，創建它
    if (!gamesDir.exists()) {
        gamesDir.mkpath(".");
    }
    
    // 設置過濾器只顯示zip檔案
    QStringList filters;
    filters << "*.zip" << "*.ZIP";
    gamesDir.setNameFilters(filters);
    
    // 獲取所有zip檔案
    QFileInfoList fileList = gamesDir.entryInfoList(QDir::Files);
    
    for (const QFileInfo &fileInfo : fileList) {
        zipFiles.append(fileInfo.absoluteFilePath());
    }
    
    return zipFiles;
}

QString GameInstaller::getGameName(const QString &zipFilePath)
{
    QFileInfo fileInfo(zipFilePath);
    return fileInfo.baseName(); // 返回不含副檔名的檔案名稱
}

bool GameInstaller::createInstallDirectory(const QString &path)
{
    QDir dir;
    if (!dir.exists(path)) {
        return dir.mkpath(path);
    }
    return true;
}

bool GameInstaller::installGame(const QString &zipFilePath, const QString &installPath)
{
    if (!QFile::exists(zipFilePath)) {
        emit installationError("ZIP檔案不存在: " + zipFilePath);
        return false;
    }
    
    // 創建安裝目錄
    if (!createInstallDirectory(installPath)) {
        emit installationError("無法創建安裝目錄: " + installPath);
        return false;
    }
    
    m_currentGameName = getGameName(zipFilePath);
    emit installationStarted(m_currentGameName);
    
    // 創建遊戲專用的安裝目錄
    QString gameInstallPath = installPath + "/" + m_currentGameName;
    if (!createInstallDirectory(gameInstallPath)) {
        emit installationError("無法創建遊戲安裝目錄: " + gameInstallPath);
        return false;
    }
    
    // 嘗試解壓
    return extractWithSystemTool(zipFilePath, gameInstallPath);
}

bool GameInstaller::isExtractionToolAvailable()
{
    // 檢查是否有PowerShell可用（Windows內建）
    QProcess process;
    process.start("powershell", QStringList() << "-Command" << "Get-Command Expand-Archive");
    process.waitForFinished(3000);
    return process.exitCode() == 0;
}

bool GameInstaller::extractWithSystemTool(const QString &zipPath, const QString &destPath)
{
    if (m_extractProcess) {
        delete m_extractProcess;
    }
    
    m_extractProcess = new QProcess(this);
    connect(m_extractProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &GameInstaller::onExtractionFinished);
    connect(m_extractProcess, &QProcess::errorOccurred,
            this, &GameInstaller::onExtractionError);
    
    // 使用PowerShell的Expand-Archive命令解壓
    QStringList arguments;
    arguments << "-Command" 
              << QString("Expand-Archive -Path '%1' -DestinationPath '%2' -Force")
                 .arg(zipPath).arg(destPath);
    
    m_extractProcess->start("powershell", arguments);
    
    return true;
}

void GameInstaller::onExtractionFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    bool success = (exitCode == 0 && exitStatus == QProcess::NormalExit);
    
    if (success) {
        emit installationProgress(100);
        emit installationFinished(m_currentGameName, true);
    } else {
        QString error = m_extractProcess->readAllStandardError();
        emit installationError("解壓失敗: " + error);
        emit installationFinished(m_currentGameName, false);
    }
    
    m_extractProcess->deleteLater();
    m_extractProcess = nullptr;
}

void GameInstaller::onExtractionError(QProcess::ProcessError error)
{
    QString errorString;
    switch (error) {
        case QProcess::FailedToStart:
            errorString = "無法啟動解壓程序";
            break;
        case QProcess::Crashed:
            errorString = "解壓程序崩潰";
            break;
        case QProcess::Timedout:
            errorString = "解壓程序超時";
            break;
        default:
            errorString = "未知錯誤";
            break;
    }
    
    emit installationError(errorString);
    emit installationFinished(m_currentGameName, false);
}
