#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QScrollArea>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QMessageBox>
#include <QTimer>
#include "gameinstaller.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class GameCard;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void refreshGameList();
    void onInstallationStarted(const QString &gameName);
    void onInstallationProgress(int percentage);
    void onInstallationFinished(const QString &gameName, bool success);
    void onInstallationError(const QString &error);

private:
    Ui::MainWindow *ui;
    GameInstaller *m_gameInstaller;
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QScrollArea *m_scrollArea;
    QWidget *m_scrollContent;
    QVBoxLayout *m_scrollLayout;
    QLabel *m_titleLabel;
    QPushButton *m_refreshButton;
    QProgressBar *m_globalProgressBar;
    QLabel *m_statusLabel;

    void setupUI();
    void setupStyles();
    void createGameCards();
    void clearGameCards();
};

// 遊戲卡片類
class GameCard : public QWidget
{
    Q_OBJECT

public:
    explicit GameCard(const QString &zipFilePath, QWidget *parent = nullptr);
    QString getZipFilePath() const { return m_zipFilePath; }

signals:
    void installRequested(const QString &zipFilePath);

private slots:
    void onInstallClicked();

private:
    QString m_zipFilePath;
    QLabel *m_gameNameLabel;
    QLabel *m_fileSizeLabel;
    QPushButton *m_installButton;

    void setupCardUI();
    QString formatFileSize(qint64 bytes);
};

#endif // MAINWINDOW_H
