#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include <QFileInfo>
#include <QDir>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_gameInstaller(new GameInstaller(this))
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_scrollArea(nullptr)
    , m_scrollContent(nullptr)
    , m_scrollLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_refreshButton(nullptr)
    , m_globalProgressBar(nullptr)
    , m_statusLabel(nullptr)
{
    ui->setupUi(this);
    setupUI();
    setupStyles();

    // 連接遊戲安裝器信號
    connect(m_gameInstaller, &GameInstaller::installationStarted,
            this, &MainWindow::onInstallationStarted);
    connect(m_gameInstaller, &GameInstaller::installationProgress,
            this, &MainWindow::onInstallationProgress);
    connect(m_gameInstaller, &GameInstaller::installationFinished,
            this, &MainWindow::onInstallationFinished);
    connect(m_gameInstaller, &GameInstaller::installationError,
            this, &MainWindow::onInstallationError);

    // 初始載入遊戲列表
    refreshGameList();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupUI()
{
    // 設置窗口屬性
    setWindowTitle("遊戲安裝器");
    setMinimumSize(800, 600);
    resize(1000, 700);

    // 創建中央widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // 創建主佈局
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setSpacing(20);
    m_mainLayout->setContentsMargins(30, 30, 30, 30);

    // 創建標題
    m_titleLabel = new QLabel("遊戲安裝器", this);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setObjectName("titleLabel");

    // 創建刷新按鈕
    m_refreshButton = new QPushButton("刷新遊戲列表", this);
    m_refreshButton->setObjectName("refreshButton");
    connect(m_refreshButton, &QPushButton::clicked, this, &MainWindow::refreshGameList);

    // 創建頂部佈局
    QHBoxLayout *topLayout = new QHBoxLayout();
    topLayout->addWidget(m_titleLabel);
    topLayout->addStretch();
    topLayout->addWidget(m_refreshButton);

    // 創建滾動區域
    m_scrollArea = new QScrollArea(this);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setObjectName("scrollArea");

    // 創建滾動內容
    m_scrollContent = new QWidget();
    m_scrollLayout = new QVBoxLayout(m_scrollContent);
    m_scrollLayout->setSpacing(15);
    m_scrollLayout->setContentsMargins(10, 10, 10, 10);
    m_scrollArea->setWidget(m_scrollContent);

    // 創建進度條
    m_globalProgressBar = new QProgressBar(this);
    m_globalProgressBar->setVisible(false);
    m_globalProgressBar->setObjectName("progressBar");

    // 創建狀態標籤
    m_statusLabel = new QLabel("準備就緒", this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setObjectName("statusLabel");

    // 添加到主佈局
    m_mainLayout->addLayout(topLayout);
    m_mainLayout->addWidget(m_scrollArea);
    m_mainLayout->addWidget(m_globalProgressBar);
    m_mainLayout->addWidget(m_statusLabel);
}
