{"artifacts": [{"path": "untitled.exe"}, {"path": "untitled.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 25, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 64, "parent": 0}, {"command": 5, "file": 1, "line": 47, "parent": 0}, {"command": 8, "file": 1, "line": 13, "parent": 0}, {"command": 8, "file": 4, "line": 297, "parent": 7}, {"file": 3, "parent": 8}, {"command": 7, "file": 3, "line": 55, "parent": 9}, {"file": 2, "parent": 10}, {"command": 6, "file": 2, "line": 61, "parent": 11}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 3, "line": 43, "parent": 9}, {"file": 9, "parent": 14}, {"command": 10, "file": 9, "line": 45, "parent": 15}, {"command": 9, "file": 8, "line": 137, "parent": 16}, {"command": 8, "file": 7, "line": 76, "parent": 17}, {"command": 8, "file": 4, "line": 315, "parent": 18}, {"file": 6, "parent": 19}, {"command": 7, "file": 6, "line": 57, "parent": 20}, {"file": 5, "parent": 21}, {"command": 6, "file": 5, "line": 61, "parent": 22}, {"command": 7, "file": 6, "line": 45, "parent": 20}, {"file": 12, "parent": 24}, {"command": 10, "file": 12, "line": 46, "parent": 25}, {"command": 9, "file": 8, "line": 137, "parent": 26}, {"command": 8, "file": 7, "line": 76, "parent": 27}, {"command": 8, "file": 4, "line": 315, "parent": 28}, {"file": 11, "parent": 29}, {"command": 7, "file": 11, "line": 55, "parent": 30}, {"file": 10, "parent": 31}, {"command": 6, "file": 10, "line": 61, "parent": 32}, {"command": 6, "file": 10, "line": 83, "parent": 32}, {"command": 9, "file": 8, "line": 137, "parent": 16}, {"command": 8, "file": 7, "line": 76, "parent": 35}, {"command": 8, "file": 4, "line": 315, "parent": 36}, {"file": 14, "parent": 37}, {"command": 7, "file": 14, "line": 55, "parent": 38}, {"file": 13, "parent": 39}, {"command": 6, "file": 13, "line": 61, "parent": 40}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 13, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 13, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 13, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 13, "define": "UNICODE"}, {"backtrace": 13, "define": "WIN32"}, {"backtrace": 13, "define": "WIN64"}, {"backtrace": 13, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 13, "define": "_UNICODE"}, {"backtrace": 13, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/OneDrive/文件/untitled/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/untitled_autogen/include"}, {"backtrace": 13, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtCore"}, {"backtrace": 13, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include"}, {"backtrace": 13, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/6.9.1/mingw_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [13, 13], "standard": "17"}, "sourceIndexes": [0, 1, 2, 5]}], "dependencies": [{"id": "untitled_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "untitled_autogen::@6890427a1f51a3e7e1df"}], "id": "untitled::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/untitled"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 6, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 12, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 23, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 23, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 33, "fragment": "-lmingw32", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\Qt\\6.9.1\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 34, "fragment": "-lshell32", "role": "libraries"}, {"backtrace": 41, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 41, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 41, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 41, "fragment": "-ld3d12", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "untitled", "nameOnDisk": "untitled.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 6, 7]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "", "sourceIndexes": [8]}, {"name": "CMake Rules", "sourceIndexes": [9]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/untitled_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "gameinstaller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "gameinstaller.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/untitled_autogen/include/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/untitled_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/untitled_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}