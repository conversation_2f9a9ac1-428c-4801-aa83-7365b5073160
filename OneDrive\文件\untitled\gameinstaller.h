#ifndef GAMEINSTALLER_H
#define GAMEINSTALLER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QDir>
#include <QFileInfo>
#include <QProcess>
#include <QProgressDialog>
#include <QMessageBox>

class GameInstaller : public QObject
{
    Q_OBJECT

public:
    explicit GameInstaller(QObject *parent = nullptr);
    
    // 掃描games資料夾中的zip檔案
    QStringList scanGameZipFiles();
    
    // 安裝遊戲（解壓zip檔案）
    bool installGame(const QString &zipFilePath, const QString &installPath = "C:/game");
    
    // 創建安裝目錄
    bool createInstallDirectory(const QString &path);
    
    // 獲取遊戲名稱（從zip檔案名稱）
    QString getGameName(const QString &zipFilePath);

signals:
    void installationStarted(const QString &gameName);
    void installationProgress(int percentage);
    void installationFinished(const QString &gameName, bool success);
    void installationError(const QString &error);

private slots:
    void onExtractionFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onExtractionError(QProcess::ProcessError error);

private:
    QProcess *m_extractProcess;
    QString m_currentGameName;
    QString m_gamesDirectory;
    
    // 檢查是否有解壓工具可用
    bool isExtractionToolAvailable();
    
    // 使用系統工具解壓
    bool extractWithSystemTool(const QString &zipPath, const QString &destPath);
};

#endif // GAMEINSTALLER_H
